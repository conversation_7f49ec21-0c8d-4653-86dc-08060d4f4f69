import {
  ANESTHESIOLOGIST_ROLES,
  CaseStaffPlanRole,
  getStaffMembersByRole,
  toRoleShortForm,
} from './roles'

const mockStaff = (id: string, role: string) => ({
  id,
  role,
  displayName: 'fake',
  firstName: 'fake',
  lastName: 'fake',
})

describe('getStaffMembersByRole', () => {
  it('displays empty planned roles if no staff', () => {
    const mockCase = {
      staff: [],
      staffPlan: [],
    }

    const result = getStaffMembersByRole(mockCase)

    expect(result).toEqual([
      { role: CaseStaffPlanRole.Anesthesiologist, staff: [] },
      { role: CaseStaffPlanRole.CRNA, staff: [] },
      { role: CaseStaffPlanRole.Circulator, staff: [] },
      { role: CaseStaffPlanRole.ScrubTech, staff: [] },
    ])
  })

  it('uses planned staff roles from staffPlan and EHR staff', () => {
    const staff = mockStaff('2', ANESTHESIOLOGIST_ROLES[1])
    const staffPlan = mockStaff('1', ANESTHESIOLOGIST_ROLES[0])
    const mockCase = {
      staff: [staff],
      staffPlan: [staffPlan],
    }

    const result = getStaffMembersByRole(mockCase)

    expect(result[0]).toEqual({
      role: CaseStaffPlanRole.Anesthesiologist,
      staff: [staffPlan, staff],
    })
  })

  it('prefers planned roles to EHR roles', () => {
    const staffPlan = mockStaff('1', 'plan')
    const mockCase = {
      staff: [mockStaff('1', 'ehr')],
      staffPlan: [staffPlan],
    }

    const result = getStaffMembersByRole(mockCase)

    expect(result[0]).toEqual({ role: 'plan', staff: [staffPlan] })
  })

  it('fills in with EHR staff roles', () => {
    const staff = mockStaff('1', 'ehr')
    const mockCase = {
      staff: [staff],
      staffPlan: [],
    }

    const result = getStaffMembersByRole(mockCase)

    expect(result[0]).toEqual({ role: 'ehr', staff: [staff] })
  })

  it('fills in with empty planned staff roles', () => {
    const staffPlan = mockStaff('1', 'plan')
    const mockCase = {
      staff: [],
      staffPlan: [staffPlan],
    }

    const result = getStaffMembersByRole(mockCase)

    expect(result).toEqual([
      { role: 'plan', staff: [staffPlan] },
      { role: CaseStaffPlanRole.Anesthesiologist, staff: [] },
      { role: CaseStaffPlanRole.CRNA, staff: [] },
      { role: CaseStaffPlanRole.Circulator, staff: [] },
    ])
  })
})

describe('toRoleShortForm', () => {
  it('abbreviates "Assistant" to "Asst" to avoid inappropriate abbreviation', () => {
    expect(toRoleShortForm('Assistant')).toBe('Asst')
    expect(toRoleShortForm('Surgical Assistant')).toBe('Surgical Asst')
    expect(toRoleShortForm('Assistant Surgeon')).toBe('Asst Surgeon')
  })

  it('handles case-insensitive "Assistant" matching', () => {
    expect(toRoleShortForm('ASSISTANT')).toBe('Asst')
    expect(toRoleShortForm('assistant')).toBe('Asst')
  })

  it('abbreviates other words normally', () => {
    expect(toRoleShortForm('Anesthesiologist')).toBe('Ane')
    expect(toRoleShortForm('Circulator')).toBe('Cir')
    expect(toRoleShortForm('Technician')).toBe('Tec')
  })

  it('keeps short words (4 chars or less) unchanged', () => {
    expect(toRoleShortForm('CRNA')).toBe('CRNA')
    expect(toRoleShortForm('Tech')).toBe('Tech')
    expect(toRoleShortForm('RN')).toBe('RN')
  })

  it('avoids inappropriate abbreviations by using 4 characters', () => {
    // Words that would create "ass" abbreviation should use 4 characters instead
    expect(toRoleShortForm('Assessment')).toBe('Asse')
  })
})
